[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
sphinx-rtd-theme = "*"
sphinxcontrib-plantuml = "*"
mistune = "*"
myst-parser = "*"
pytest = "*"
pytest-bdd = "*"
pytest-split = "*"
dynaconf = "*"
isort = "*"
black = "*"
flake8 = "*"
pretend = "*"
names-generator = "*"
pytest-bdd-html = "*"
pytest-html = "*"
sphinx-toolbox = "*"
py = "*"
sphinx = "*"
sphinx-needs = "*"

[dev-packages]

[requires]
python_version = "3.13"

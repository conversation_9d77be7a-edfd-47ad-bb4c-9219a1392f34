# .readthedocs.yaml
# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

submodules:
  include: all

# Set the version of Python and other tools you might need
build:
  os: ubuntu-20.04
  tools:
    python: "3.13"
  apt_packages:
    - plantuml
  jobs:
    pre_create_environment:
      - pip install pipenv
      - pipenv requirements --dev > requirements.txt 

# Build documentation in the docs/ directory with Sphinx
sphinx:
  configuration: docs/source/conf.py

# Optionally declare the Python requirements required to build your docs
python:
  install:
    - requirements: requirements.txt

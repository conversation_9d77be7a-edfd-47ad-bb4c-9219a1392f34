version: "3.7"

volumes:
  rstuf-api-data:
  rstuf-mq-data:
  rstuf-storage:
  rstuf-redis-data:
  rstuf-pgsql-data:

services:
  redis:
    image: redis:8.0.0-alpine3.21
    volumes:
      - rstuf-redis-data:/data
    healthcheck:
      test: "exit 0"
    restart: always
    tty: true

  postgres:
    image: postgres:17.5-alpine3.21
    ports:
      - "5433:5432"
    # DO NOT USE IT IN PRODUCTION. Check the Postgres best practices
    environment:
      - POSTGRES_PASSWORD=secret
    volumes:
      - "rstuf-pgsql-data:/var/lib/postgresql/data"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres", "-d", "postgres"]
      interval: 1s

  rstuf-worker:
    image: ghcr.io/repository-service-tuf/repository-service-tuf-worker:latest
    volumes:
      - rstuf-storage:/var/opt/repository-service-tuf/storage
      - ./local-keyvault/:/var/opt/repository-service-tuf/keyvault/ # map the path where is your key
    environment:
      RSTUF_STORAGE_BACKEND: LocalStorage
      RSTUF_LOCAL_STORAGE_BACKEND_PATH: /var/opt/repository-service-tuf/storage
      RSTUF_ONLINE_KEY_DIR: /var/opt/repository-service-tuf/keyvault
      RSTUF_BROKER_SERVER: redis://redis
      RSTUF_REDIS_SERVER: redis://redis
      RSTUF_SQL_SERVER: ******************************************

    depends_on:
      - postgres
      - redis
    healthcheck:
      test: "exit 0"
    restart: always
    tty: true

  web-server:
    image: python:3.13-slim
    command: python -m http.server -d /www 8080
    volumes:
      - rstuf-storage:/www
    ports:
      - "8080:8080"

  rstuf-api:
    image: ghcr.io/repository-service-tuf/repository-service-tuf-api:latest
    volumes:
      - rstuf-api-data:/data
    ports:
      - 80:80
      - 443:443
    environment:
      RSTUF_BROKER_SERVER: redis://redis
      RSTUF_REDIS_SERVER: redis://redis
    depends_on:
      - rstuf-worker

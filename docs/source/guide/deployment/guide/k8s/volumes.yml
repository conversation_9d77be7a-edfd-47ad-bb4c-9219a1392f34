##########################################
# VOLUMES
##########################################
# RSTUF Storage (backend storage)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: rstuf-storage
  name: rstuf-storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Mi
status: {}
---
# Redis persistent data volume
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: redis-data
  name: redis-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Mi
status: {}
---
# Postgres persistent data volume
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: postgres-data
  name: postgres-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 250Mi
status: {}

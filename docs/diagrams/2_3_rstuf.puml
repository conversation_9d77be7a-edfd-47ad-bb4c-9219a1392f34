@startuml 2_3_rstuf
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Servers/database_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>
!include <office/Servers/cluster_server>

AddPersonTag("project_mantainers", $sprite="osa_user_large_group", $legendText="Project Mantainers", $bgColor=Gray)
AddPersonTag("admin", $sprite="osa_user_audit,color=yellow", $legendSprite="osa_user_audit,scale=0.25,color=yellow", $legendText="Repository Admins", $bgColor=Gray)
AddPersonTag("repository_users", $sprite="osa_user_green_developer", $legendSprite="osa_user_green_developer,scale=0.25,color=yellow", $legendText="Repository Users", $bgColor=Gray)

AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rstuf_rest_api", $sprite="web_server", $legendText="Repository REPOSITORY-SERVICE-TUF-API")
AddContainerTag("rstuf_repo_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Broker")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("download_artifact", $sprite="download", $legendText="Download file/artifact/package", $bgColor=Gray)
AddContainerTag("queue_cluster", $sprite="cluster_server", $legendText="Broker Cluster")
AddContainerTag("db_db", $sprite="database_server", $legendText="db Server")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=1.0,color=#438DD5", $legendText="CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="orange", $lineStyle = DashedLine())
AddRelTag("sync", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $lineStyle="---")

Boundary(tenant, "Tenant"){

    Person(users_1, "Users", $tags="project_mantainers") #Grey
    Person(users_2, "Users", $tags="project_mantainers") #Grey

    Container(automation, "Automations", "CI/CD, Artfact Systems, ...", $tags="ci_cd") #Grey
    Container(queue, "Broker Federation", "Broker Cluster", $tags="queue")  #Grey

    Boundary(region_1, "Region 1") {
        Boundary(load_balance_r1, "Load Balance") {
            Container(webui_1, "WebUI", $tags="webui") #Grey
            Container(webui_2, "WebUI", $tags="webui") #Grey
            Lay_R(webui_1, webui_2)
        }
        Boundary(apis_r1, "REPOSITORY-SERVICE-TUF-API"){
            Container(api_gateway_r1, "API Gateway", "Local/SaaS API Gateway", "Security, Authentication, etc") #Grey
            Container(rstuf_rest_api_1, "REPOSITORY-SERVICE-TUF-API", "Python", $tags="rstuf_rest_api") #DodgerBlue
            Container(rstuf_rest_api_2, "REPOSITORY-SERVICE-TUF-API", "Python", $tags="rstuf_rest_api") #DodgerBlue
            Lay_R(api_gateway_r1, rstuf_rest_api_1)
            Lay_R(rstuf_rest_api_1, rstuf_rest_api_2)
            Rel(api_gateway_r1, rstuf_rest_api_1,)
            Rel(api_gateway_r1, rstuf_rest_api_2,)
        }
        Container(queue_service_r1, "Broker", "Region 1 Cluster", $tags="queue_cluster")  #Grey
        Boundary(repository_workers_r1, "Repository Workers") {
            Container(rstuf_repo_worker_1, "REPOSITORY-SERVICE-TUF-WORKER 1", "consumer", $tags="rstuf_repo_worker") #DodgerBlue
            Container(rstuf_repo_worker_2, "REPOSITORY-SERVICE-TUF-WORKER 2", "consumer", $tags="rstuf_repo_worker") #DodgerBlue
            Lay_R(rstuf_repo_worker_1, rstuf_repo_worker_2)
        }

        Rel(automation, api_gateway_r1, "HTTPS")
        Rel(queue, queue_service_r1, "Sync", $tags="sync")

        Rel(webui_1, api_gateway_r1, "HTTPS")
        Rel(webui_2, api_gateway_r1, "HTTPS")

        Rel_D(rstuf_rest_api_1, queue_service_r1, "Tasks")
        Rel_D(rstuf_rest_api_2, queue_service_r1, "Tasks")

        Rel_U(rstuf_repo_worker_1, queue_service_r1, "Consumer")
        Rel_U(rstuf_repo_worker_2, queue_service_r1, "Consumer")
    }

    Boundary(region_2, "Region 2") {
        Boundary(load_balance_r2, "Load Balance") {
            Container(webui_3, "WebUI", $tags="webui") #Grey
            Container(webui_4, "WebUI", $tags="webui") #Grey
            Lay_R(webui_3, webui_4)
        }
        Boundary(apis_r2, "REPOSITORY-SERVICE-TUF-API"){
            Container(api_gateway_r2, "API Gateway", "Local/SaaS API Gateway", "Security, Authentication, etc") #Grey
            Container(rstuf_rest_api_3, "REPOSITORY-SERVICE-TUF-API", "Python", $tags="rstuf_rest_api") #DodgerBlue
            Container(rstuf_rest_api_4, "REPOSITORY-SERVICE-TUF-API", "Python", $tags="rstuf_rest_api") #DodgerBlue
            Lay_R(api_gateway_r2, rstuf_rest_api_3)
            Lay_R(rstuf_rest_api_3, rstuf_rest_api_4)
            Rel(api_gateway_r2, rstuf_rest_api_3,)
            Rel(api_gateway_r2, rstuf_rest_api_4,)
        }
        Container(queue_service_r2, "Broker", "Region 1 Cluster", $tags="queue_cluster")  #Grey
        Boundary(repository_workers_r2, "Repository Workers") {
            Container(rstuf_repo_worker_3, "REPOSITORY-SERVICE-TUF-WORKER 1", "consumer", $tags="rstuf_repo_worker") #DodgerBlue
            Container(rstuf_repo_worker_4, "REPOSITORY-SERVICE-TUF-WORKER 2", "consumer", $tags="rstuf_repo_worker") #DodgerBlue
            Lay_R(rstuf_repo_worker_3, rstuf_repo_worker_4)
        }


        Rel(automation, api_gateway_r2, "HTTPS")
        Rel(queue, queue_service_r2, "Sync")

        Rel(webui_3, api_gateway_r2, "HTTPS")
        Rel(webui_4, api_gateway_r2, "HTTPS")

        Rel_D(rstuf_rest_api_3, queue_service_r2, "Tasks")
        Rel_D(rstuf_rest_api_4, queue_service_r2, "Tasks")

        Rel_U(rstuf_repo_worker_3, queue_service_r2, "Consumer")
        Rel_U(rstuf_repo_worker_4, queue_service_r2, "Consumer")
    }

    ContainerDb(key_service, "Key Service", "Local/SaaS Vault", $tags="key_service") #Grey
    ContainerDb(storage_service, "Storage Service", "Local/SaaS Storage", $tags="storage_service") #Grey

    Rel(users_1, api_gateway_r1, "REPOSITORY-SERVICE-TUF-CLII", $tags="terminal")
    Rel(users_1, load_balance_r1, "REPOSITORY-SERVICE-TUF-CLII", $tags="browser")

    Rel(users_2, api_gateway_r2, "REPOSITORY-SERVICE-TUF-CLII", $tags="terminal")
    Rel(users_2, load_balance_r2, "REPOSITORY-SERVICE-TUF-CLII", $tags="browser")

    Rel_D(rstuf_repo_worker_1, storage_service, "read/write")
    Rel_D(rstuf_repo_worker_2, storage_service, "read/write")
    Rel_D(rstuf_repo_worker_1, key_service, "read/write")
    Rel_D(rstuf_repo_worker_2, key_service, "read/write")
    Rel_D(rstuf_repo_worker_3, storage_service, "read/write")
    Rel_D(rstuf_repo_worker_4, storage_service, "read/write")
    Rel_D(rstuf_repo_worker_3, key_service, "read/write")
    Rel_D(rstuf_repo_worker_4, key_service, "read/write")
    }

HIDE_STEREOTYPE()

@enduml
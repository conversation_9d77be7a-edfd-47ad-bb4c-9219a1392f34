@startuml 2_1_rstuf
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Servers/database_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>
!include <tupadr3/devicons/redis>


AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rest_api", $sprite="web_server", $legendText="Repository REST API")
AddContainerTag("repo_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("db", $sprite="database_server", $legendText="Postgredb")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("redis", $sprite="redis", $legendText="Redis")
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=0.75,color=#438DD5", $legendText="Repository Service for TUF CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="grey", $lineStyle = DashedLine())


Container(rstuf_cli, "REPOSITORY-SERVICE-TUF-CLI", "Python Application", $tags="terminal") #DodgerBlue
System_Boundary(rstuf, "Repository Service for TUF ") {
    Container(rstuf_rest_api, "REPOSITORY-SERVICE-TUF-API", "Docker", $tags="rest_api") #DodgerBlue
    Container(rstuf_repo_worker, "REPOSITORY-SERVICE-TUF-WORKER", "Docker", $tags="repo_worker") #DodgerBlue
}
Container(queue, "Broker/Backend", "RabbitMQ, Redis, etc", $tags="queue") #SlateGray
Container(redis, "Redis", "Redis", $tags="redis") #SlateGray
ContainerDb(key_service, "Key Service", "Local/SaaS Vault", $tags="key_service") #LightGrey
ContainerDb(storage_service, "Storage Service", "Local/SaaS Storage", $tags="storage_service") #LightGrey
ContainerDb(db, "DB Server", "DB Server", $tags="db") #SlateGray

Rel(rstuf_cli, rstuf_rest_api, "REPOSITORY-SERVICE-TUF-CLI", "HTTP(S)")
Rel_U(queue, rstuf_repo_worker, "Consumer", "Tasks")
Rel_U(key_service, rstuf_repo_worker, "Read", "Online Keys")
BiRel_U(redis, rstuf_repo_worker, "Read/Write", "Settings")
Rel_D(rstuf_rest_api, queue, "Producer", "Tasks")
Rel_D(redis, rstuf_rest_api, "Read", "Settings")
BiRel_D(rstuf_repo_worker, storage_service, "Read/Write", "Repository Metadata")
BiRel_U(rstuf_repo_worker, db, "Read/Write", "TargetFiles, BINSRoles")


HIDE_STEREOTYPE()

@enduml
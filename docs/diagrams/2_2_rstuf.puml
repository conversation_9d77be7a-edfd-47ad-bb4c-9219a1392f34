@startuml 2_2_rstuf
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Servers/database_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>


AddPersonTag("admin", $sprite="osa_user_audit,color=yellow", $legendSprite="osa_user_audit,scale=0.25,color=yellow", $legendText="Repository Admins", $bgColor=Gray)
AddPersonTag("user", $sprite="osa_user_green_developer", $legendSprite="osa_user_green_developer,scale=0.25,color=yellow", $legendText="Repository Users", $bgColor=Gray)

AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rest_api", $sprite="web_server", $legendText="Repository REST API")
AddContainerTag("repo_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("download_artifact", $sprite="download", $legendText="Download file/artifact/package", $bgColor=Gray)
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")
AddContainerTag("db", $sprite="database_server", $legendText="db Server")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=0.75,color=#438DD5", $legendText="Repository Service for TUF CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="grey", $lineStyle = DashedLine())

Person(administration_user, "Admin", $tags="admin") #Grey
Person(repository_user, "User", $tags="user")

System_Boundary(build_systems, "CD/Build Systems") {
    System_Boundary(rstuf, "Repository Service for TUF ") {
        Container(rstuf_rest_api, "REPOSITORY-SERVICE-TUF-API", "Docker", $tags="rest_api") #DodgerBlue
        Container(rstuf_repo_worker, "REPOSITORY-SERVICE-TUF-WORKER", "Docker", $tags="repo_worker") #DodgerBlue
    }
    Container(queue, "Broker/Backend", "RabbitMQ, Redis, etc", $tags="queue") #Grey
    Container(ci_cd, "CI/CD", "Jenkis, Github, Gitlab, etc", $tags="ci_cd") #Grey
    ContainerDb(db, "DB Server", "db Server", $tags="db") #Grey
    ContainerDb(key_service, "Key Service", "Local/SaaS Vault", $tags="key_service") #Grey
    ContainerDb(storage_service, "Storage Service", "Local/SaaS Storage", $tags="storage_service") #Grey
}

System_Boundary(files, "Download / Repository files") {
    Container(webserver, "Metadata", "Optional", "TUF Metadata (Repository Service for TUF )", $tags="metadata_web") #Grey
    Container(repository, "Repository", "FTP, HTTP, NPN, PyPI, JFrog Artifactory, etc", "Files/Artificts", $tags="metadata_web") #Grey
}

Rel(administration_user, rstuf_rest_api, "REPOSITORY-SERVICE-TUF-CLI", $tags="terminal")
Rel_R(ci_cd, rstuf_rest_api, "HTTPS")
Rel_U(queue, rstuf_repo_worker, "Consumer", "Tasks")
Rel_D(rstuf_rest_api, queue, "Producer", "Tasks")
Rel(db, rstuf_repo_worker, "db", "TargetFiles")
Rel_U(key_service, rstuf_repo_worker, "Read", "Online Keys")
Rel_D(rstuf_repo_worker, storage_service, "Read/Write", "Repository Metadata")

Rel_D(storage_service, webserver, "Render/Share/View", $tags="share")
Rel_D(ci_cd, repository, "Upload")
Rel_L(files, repository_user, "Download")

Lay_R(repository, webserver)

HIDE_STEREOTYPE()
@enduml
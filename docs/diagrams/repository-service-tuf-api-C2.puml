@startuml repository-service-tuf-api-C2
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>


sprite $rstuf [125x125/16z] {
xC610000082W_QaNgG8009WTCz6QL8M1S585A8ha_T_sYavpCdYEtVwptUsRzNUX81N3yejHXZJDESzJ2b3vZI1cCq-EaD-2XiNCQYbRRRfrAwKs_I2_dbZC
jdBgbdLz_MypAxzQdgjP4x8v4jgyLZ3ErZfajb9-7Jhj8hlq77IqOZxlJ3zHZ76QbvppVAGqoYa0yQWSuEpO2laP82tcsUPgMxKCyR1nwcEnkfbHVXZcfjSo
kb6tm-ZDB0pX9WW6o44q-mdgN9goDTPbfocte6uOdYEO0lZA0TORzMTvLUTNyL9bCs4PyuoCKsGa8IUwDdN8LtdbrJAcD2W3858A04dnwaNbYz1QNYsKzu08
JTU9Yswx-YnV8nn94-KJbAHIGK8wQ3aVlgXTDoitfc5UPxWF3P4WLNmJcsqgps7JpdgZRGtqeMlJ1yGpx-f97gCjvbckU4StDp0S_-7OIgscyfIyQoyzc-Yy
T8Codr5KaI03oShpeJUtJBkT10F54vkTeULpbAxUz2z3jw9V23hb00684I3aaiYXVOlzUd0gVkYKHqGhVKuamvH22AHqjDXsF32zAoHe3puTU0-w466A1E62
sZHNQQpl2ibYLkG9qQT900CXH8-gY_RxGf9iUrRX5wkcldm06iZ1yA4Z3dNVEwqygdBmeHIG2C4NphNxw2nmC90XG0xqeUdn-yvIkQFqQjx6CZHF0cvcBsPP
xXdhXqgdoQfGnpYFBhYI55FeyrisM-LpLhDbdBu1BnM7IFdka9mJ6KcLnlmetQyk0M1FHfSGFbHB5mTIf01Tl5Pzb6zHn4Dl-7MML6yxTm0Xk_Y3kUNkuxWQ
VhKTJPiWAJtipyGf3Y_YekA9VY826GX2lETV5xyn_6gxGW5LzdUE6y_3hZgKoglXVhMOtBhYfQsrFbb5ufkuCZ0GlbVRPfIHwqswie1dAobjZ_SD4Bkw3eRV
sUuceMUrNTlMzzi6A8AzdNopgXllOoYNdvFDjhu12gHqmqtVBN_IvUApV4kjP403OWfGacyD7sqVb-vBJlpqjEEetXhS1iWDwmON7Ez5jSRwNZvPw_rEb42_
0KetXdkftaDLFIrlj4O_wVfcLd9EumOeMxKy7IJ82D9qS3mb_ZFLEU-K9j_LRoftffpGRu2cTki5BsOVd8PDupb5W_dTMa-hzrKKRWE4e-a3lshgwwfwO3iO
pobyFjmldtmb9Dm6Y4lT3cgV8lssv7Npo2ibqnGOIVZgN-xsstbibg0wZODcm4My1LiFf06wpzHryIPbtHHmkm7guhgggySE404KTIE_MT8yc0ASUAgJ4x-U
Kv34KFnn9nu_5zTvRh-xk8SxaJjoApvQzwbup2Ms1ajEetW_fr2eE8vakLiobq_5Iwy6HQ_Y0XnT44-vNS2dOUgz4r_wMU_InJt4g1tk3ocQwOOFnLLsK5LM
B-xLmz3uZUKlLony8juaWw1IT7MUUyPnlyietPhw4dd7kGbO8ElKZCTob8-P3PpPvcXN4E9Yxu_aK8zfxPxQn5CKdIaYiMNxhduIZX5ueiHlSsg5tAFpSQH-
nrdRiSh9YhEsbuPb4ThgdhP5iXs7zRFAMpX_D7APaZJI9EF8op3oO3ipvICK7tWkKeA8X2BIFMSOFJ-lW51vp7rvBL5YSUNrDz35HNF9Adz4l155_fou_Fl5
_nV_NvpguUyL7zaioc-1jbqnoEyQ-fKalml4z3qHyY_XVm3v7ENpj-ESauh3aIUt_FOmhvCCnABYOLty1In_jJyTYPQ48B28QglS_xW3EZdzrTzrlKHJwT7R
5CcTjoeTHVQbgoUdd7gI_2QLykOY1mkj-P8Y14wIeedVDPrrpinMANBNYROKA0SnbSV6SjQWR8I7wT4C6WCMleNYMK7vlLlIpJ4RhsUInHDC-MMX2A_ope0l
3-RqMB5Y0Ocvw4G1lERWiTQ4vXO9dy0fVzBATChtMemvD1g5KgW5QLG4uMbewTYU4wr6Q4sYpfALzEefuZ32CjsmJHcB6rLT6odc40czJUj7-OvvBxSuvIef
FELbWiw7HhlbXj5fQvNsWGCFURegSqfXpt3OeYBfH9LYGOAeoWa_BcUCyVtolZ0sUQWpnJFglxRvm6miUb8MFSaMIc40HcX-NqdRwdLPFGUd4XfjfDhyb0Te
ZnvKXOMsIpaheiQ26iWPsfmet2hAZI2jbfgS4xJIh2BiNc4bkPwaKKA88OLwrxZFwMRmURmBiBdSyJVK5V-7X-EJGFsZycRu2rwyUF7YnOiNNm
}

AddPersonTag("admin", $sprite="osa_user_audit,color=yellow", $legendSprite="osa_user_audit,scale=0.25,color=yellow", $legendText="Repository Admins", $bgColor=Gray)
AddPersonTag("user", $sprite="osa_user_green_developer", $legendSprite="osa_user_green_developer,scale=0.25,color=yellow", $legendText="Repository Users", $bgColor=Gray)

AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rest_api", $sprite="rstuf", $legendText="Repository REST API")
AddContainerTag("repo_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("download_artifact", $sprite="download", $legendText="Download file/artifact/package", $bgColor=Gray)
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=0.75,color=#000000", $legendText="Repository Service for TUF CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="grey", $lineStyle = DashedLine())

Person(administration_user, "Admin", $tags="admin") #Grey

Container(rstuf_rest_api, "REPOSITORY-SERVICE-TUF-API", $tags="rest_api")
Container_Ext(broker, "Broker/Backend", "RabbitMQ, Redis, etc", $tags="queue") #Grey
Container_Ext(ci_cd, "Third Part Software", "Jenkis, Github, Gitlab, etc", $tags="ci_cd") #Grey

Rel(administration_user, rstuf_rest_api, "REPOSITORY-SERVICE-TUF-CLI", $tags="terminal")
Rel_R(ci_cd, rstuf_rest_api, "HTTPS + Tokens")
Rel_D(rstuf_rest_api, broker, "Publisher/Consumer", "Tasks")


HIDE_STEREOTYPE()
@enduml
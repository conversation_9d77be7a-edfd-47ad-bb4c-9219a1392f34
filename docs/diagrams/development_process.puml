@startuml
allowmixing
package "RSTUF Organization" {

    object "Projects" as projects #Lime{
            Goal 01
            Goal 02
    }

    package "repository-service-tuf" {
        json ROADMAP {
            "Goal 01": {
                "features": ["Feature A", "Feature B"],
                "milestones": {
                    "repository-service-tuf-api": "v1.2.0",
                    "repository-service-tuf-worker": "v1.1.0",
                    "repository-service-tuf-cli": "v1.4.0"
                }
            },
            "Goal 02": {
                "features": ["Feature C", "Feature D"],
                "milestones": {
                    "repository-service-tuf-api": "v1.3.0",
                    "repository-service-tuf-worker": "v1.1.0",
                    "repository-service-tuf-cli": "v2.0.0"
                }
            }
        }

        object Issues #RoyalBlue {
            Feature A
            Feature B
            Feature C
            Feature D
        }

    }

    package "repository-service-tuf-api" {
        json "Issues" as api_issues {
            "issue#12": {
                "milestone": "v1.1.3",
                "projects": "Goal 01"
            },
            "issue#14": {
                "milestone": "v1.2.2",
                "projects": null
            },
            "issue#46": {
                "milestone": "v1.2.0",
                "projects": "Goal 02"
            },
            "issue#30": {
                "milestone": "v1.2.0",
                "projects": "Goal 01"
            }
        }
        object "Milestones" as api_milestones {
            v1.1.3
            v1.2.0
            v1.2.1
        }

        api_issues -> api_milestones
    }
    package "repository-service-tuf-worker" {
        json "Issues" as repo_issues {
            "issue#31": {
                "milestone": "v1.1.0",
                "projects": ["Goal 01", "Goal 02"]
            },
            "issue#32": {
                "milestone": "v1.1.0",
                "projects": ["Goal 01", "Goal 02"]
            },
            "issue#55": {
                "milestone": "v1.2.0",
                "projects": null
            },
            "issue#42": {
                "milestone": "v1.2.0",
                "projects": null
            }
        }
        object "Milestones" as repo_milestones {
            v1.1.0
            v1.0.9
            v1.2.0
        }
        repo_issues -> repo_milestones
    }
    ROADMAP --> Issues
    projects --> ROADMAP
    Issues --D-> api_issues
    Issues --D-> repo_issues
    api_milestones --[#grey,dotted]-> ROADMAP
    repo_milestones --[#grey,dotted]-> ROADMAP
}

@enduml

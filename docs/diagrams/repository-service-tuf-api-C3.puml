@startuml repository-service-tuf-api-C3
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>


AddPersonTag("admin", $sprite="osa_user_audit,color=yellow", $legendSprite="osa_user_audit,scale=0.25,color=yellow", $legendText="Repository Admins", $bgColor=Gray)
AddPersonTag("user", $sprite="osa_user_green_developer", $legendSprite="osa_user_green_developer,scale=0.25,color=yellow", $legendText="Repository Users", $bgColor=Gray)

AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rest_api", $sprite="web_server", $legendText="Repository REST API")
AddContainerTag("repo_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("download_artifact", $sprite="download", $legendText="Download file/artifact/package", $bgColor=Gray)
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=0.75,color=#000000", $legendText="Repository Service for TUF CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="grey", $lineStyle = DashedLine())

Person(administration_user, "Admin", $tags="admin") #Grey

System_Boundary(rstuf_rest_api, "REPOSITORY-SERVICE-TUF-API") #APPLICATION {
    Container_Boundary(app, "app")  #LightSkyBlue {
        Container_Boundary(api, "api") #DeepSkyBlue{
            Container(api_config, "config")
            Container(api_bootstrap, "bootstrap")
            Container(api_artifacts, "artifacts")
            Container(api_tasks, "tasks")
        }
        Container(config, "config")
        Container(bootstrap, "bootstrap")
        Container(artifacts, "artifacts")
        Container(tasks, "tasks")
        Container(metadata, "metadata")

        Container_Ext(celery, "Celery()")
        Container_Ext(dynaconf, "Dynaconf()")
    }
}
' Container(rstuf_rest_api, "REPOSITORY-SERVICE-TUF-API", $tags="rest_api")
Container(broker, "Broker", "RabbitMQ, Redis, etc", $tags="queue") #Grey
Container(redis, "Redis", "Redis Server", $tags="queue") #Grey
Container(ci_cd, "Third Part Software", "Jenkis, Github, Gitlab, etc", $tags="ci_cd") #Grey

Rel(administration_user, rstuf_rest_api, "REPOSITORY-SERVICE-TUF-CLI", $tags="terminal")
Rel(ci_cd, rstuf_rest_api, "HTTPS + Tokens")


Rel(api_bootstrap, bootstrap, " ")
Rel(api_config, config, " ")
Rel(api_artifacts, artifacts, " ")
Rel(api_tasks, tasks, " ")
Rel(artifacts, metadata, " ")
Rel(bootstrap, metadata, " ")
Rel(tasks, metadata, " ")
Rel(bootstrap, config, " ")
Rel(metadata, celery, " ")
Rel(config, dynaconf, " ")
Rel(celery, broker, "Publisher")
Rel_D(celery, redis, "Publisher, Consumer")
Rel_D(dynaconf, redis, "Publisher, Consumer")

Lay_R(bootstrap, artifacts)
Lay_R(dynaconf, celery)

HIDE_STEREOTYPE()
@enduml
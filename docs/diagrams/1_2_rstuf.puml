@startuml 1_2_rstuf
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

AddRelTag("validate", $textColor="orange", $lineColor="Blue", $lineStyle = DashedLine())
AddRelTag("download", $textColor="orange", $lineColor="Blue")


Person(admin, "Repository Admin")
Person(user, "Downloader User") #Grey

System(repository, "Repository Service for TUF ", "Signed Metadata")
System(automation, "CI/CD, Artifact Systems", "Third part sofware") #Grey
System(files_repository, "Repository", "Targets/Artifacts") #Grey

Rel_L(admin, repository, "CLI/API", "", "- Bootstrap Ceremony\n- Manage Keys\n- Repository Operations")
Rel(automation, repository, "API")
Rel(automation, files_repository, "Upload file")
Rel_U(user, repository, "1", "", "Validate", $tags="validate")
Rel_U(user, files_repository, "2", "", "Install, Download Update", $tags="download")

HIDE_STEREOTYPE()
@enduml
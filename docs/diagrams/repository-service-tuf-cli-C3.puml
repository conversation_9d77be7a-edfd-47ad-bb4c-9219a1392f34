@startuml repository-service-tuf-cli-C3
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>


AddPersonTag("admin", $sprite="osa_user_audit,color=yellow", $legendSprite="osa_user_audit,scale=0.25,color=yellow", $legendText="Repository Admins", $bgColor=Gray)
AddPersonTag("user", $sprite="osa_user_green_developer", $legendSprite="osa_user_green_developer,scale=0.25,color=yellow", $legendText="Repository Users", $bgColor=Gray)

AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rest_api", $sprite="web_server", $legendText="Repository REST API")
AddContainerTag("repo_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("download_target", $sprite="download", $legendText="Download file/target/package", $bgColor=Gray)
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=0.75,color=#000000", $legendText="Repository Service for TUF CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="grey", $lineStyle = DashedLine())
AddRelTag("os", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $lineStyle = DashedLine(), $sprite="file_server,scale=0.75,color=#000000", $legendText="Writes OS")

Person(user, "Admin", $tags="admin") #Grey

System_Boundary(trs_cli, "Repository Service for TUF CLI") #APPLICATION {
    Container_Boundary(repository_service_tuf, "repository_service_tuf")  #LightSkyBlue {
        Container_Boundary(cli, "cli") #DeepSkyBlue{
            Container_Ext(click, "click")
            Container_Boundary(admin, "admin") {
                Container(ceremony, "ceremony")
                Container_Boundary(metadata, "metadata"){
                    Container(update, "update")
                    Container(sign, "sign")

                }
                Container_Boundary(send, "send"){
                    Container(bootstrap, "bootstrap")
                    Container(update, "update")
                    Container(sign, "sign")

                }
            }
            Container_Boundary(key, "key") {
                Container(key_generate, "generate")
                Container(info, "info")
            }
        }
        Container_Boundary(users, "helpers",) #LightBlue {
            Container(api_client, "api_client")
            Container_Boundary(tuf, "tuf",) #APPLICATION {
                Container_Ext(python_tuf, "python-tuf")
            }

        }
        Container_Ext(dynaconf, "Dynaconf()")
        Container_Ext(requests, "requests")
    }
}
Container(tuf_repository_service_api, "REPOSITORY-SERVICE-TUF-API", "HTTP REST API", $tags="queue") #APPLICATION

Rel_R(user, trs_cli, "rstuf-cli", $tags="terminal")

Rel(ceremony, tuf, " ")
Rel(info, tuf, " ")
Rel(ceremony, api_client, " ")
Rel(metadata, tuf, " ")
Rel(metadata, api_client, " ")
Rel(send, api_client, " ")
Rel(api_client, requests, " ")
Rel(dynaconf, user, " ", $tags="os")
Rel_U(ceremony, click, " ")
Rel_U(key_generate, click, " ")
Rel_U(info, click, " ")
Rel_U(metadata, click, " ")
Rel_U(send, click, " ")
Rel(requests, tuf_repository_service_api, " ")

HIDE_STEREOTYPE()
@enduml
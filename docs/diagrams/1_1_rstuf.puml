@startuml 1_1_rstuf
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml
!include <office/Servers/file_server>
!include <office/Concepts/service_application>
!include rstuf_icon.puml

AddPersonTag("users", $sprite="osa_user_large_group", $legendText="aggregated user")

AddRelTag("validate", $textColor="orange", $lineColor="Blue", $lineStyle = DashedLine())
AddRelTag("download", $textColor="blue", $lineColor="Blue")
AddRelTag("fetch", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="service_application,scale=0.75,color=#438DD5", $legendText="Fetch")

AddContainerTag("files", $sprite="file_server", $legendText="file server container")
AddContainerTag("tuf_updater", $sprite="service_application,scale=0.5,color=#000000", $legendText="TUF Updater")
AddContainerTag("ci_cd", $sprite="service_application,scale=1.4", $legendText="CI/CD")
AddContainerTag("rstuf", $sprite="rstuf,scale=1.0", $legendText="Repository Service for TUF ")

Person(users, "Users", $tags="users") #Grey
Person(developers, "Developers", $tags="users") #Grey

System_Boundary(build_systems, "Build System") {
    Container(rstuf, "RSTUF", $tags=rstuf)
    Container(ci_cd, "CI/CD, Artifact Systems, Distribution Platform", "Jenkins, Github Action, Gitlab, CircleCI, JFrog Artifactory, Custom Solution, etc", $tags=ci_cd) #Grey
}

Container(files_repository, "Repository Files", "Metadata and Artifacts", "HTTPS, FTP , JFrog Artifactory, NPN, etc", $tags=files) #Grey


Rel_L(developers, ci_cd, "New Release")
Rel_L(ci_cd, rstuf, "API")
Rel(ci_cd, files_repository, "Upload file")
Rel(rstuf, files_repository, "Upload Metadata")
Rel_R(files_repository, users, "Download/Update/Install", "Verify & Fetch", $tags="fetch")


HIDE_STEREOTYPE()
@enduml
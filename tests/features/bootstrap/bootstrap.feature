Feature: Bootstrap Repository Service for TUF (RSTUF)
    As an admin,
    <PERSON><PERSON> has done the offline Ceremony to generate the payload.json,
    <PERSON><PERSON> wants to bootstrap the RSTUF uploading the file

    Scenario: Bootstrap using RSTUF Command Line Interface (CLI)
        Given the repository-service-tuf (rstuf) is installed
        When the admin run rstuf for ceremony bootstrap
        Then the admin gets "Bootstrap status: SUCCESS" and "Bootstrap finished." or "System already has a Metadata"

    Scenario: Bootstrap using RSTUF Command Line Interface (CLI) with invalid payload
        Given the repository-service-tuf (rstuf) is installed
        When the admin run rstuf for ceremony bootstrap with invalid payload JSON
        Then the admin gets "Error 422" or "System LOCKED for bootstrap."
######################################
Technical Advisory Council (RSTUF-TAC)
######################################

The role of the Technical Advisory Council (TAC) is to provide subject matter
expertise in software repository design, to make recommendations and guide the
future activities of the technical community.

Get Involved
############

If you are maintaining a public repository and would like to join the
discussion, just open a PR adding your details in the TAC Members table.
TAC members will review it at the next meeting.

Meetings
########

The TAC members meet (and vote if there is a quorum) on-demand as part of the
`RSTUF Community Meeting <https://repository-service-tuf.readthedocs.io/en/stable/devel/contributing.html#meetings>`_.

TAC Roles and Responsibilities
##############################

Voting Member
=============

* Shares use cases
* Provides subject matter expertise on software repository design
* Votes on user/repository features priority in the roadmap (each organization
  has one vote)

.. note::

  * Functional features, such as support, stability, securing, strength,
    behavior, etc., have higher priority over user/repository features.

RSTUF-TAC Members
#################

.. list-table:: RSTUF-TAC Members
    :header-rows: 1
    :widths: 20 20 40 20

    * - Representative
      - Role
      - Organization
      - Github
    * - | Kairo de Araujo
        | Martin Vrachev
        | Konstantinos Papadopoulos
        | Lukas Pühringer
      - Voting Member
      - RSTUF Maintainer
      - | @kairoaraujo
        | @MVrachev
        | @KAUTH
        | @lukpueh
    * - Josef Šimánek
      - Voting Member
      - | RubyGems.org maintainer
        | OpenSSF/Securing Software Repo WG
      - @simi
    * - Ee Durbin
      - Voting Member
      - | PyPI Maintainer
        | Python Software Foundation
      - @ewdurbin
    * - `<name>`
      - Voting Member
      - The Update Framework Maintainer
      - `@github account`
    * - `<name>`
      - Voting Member
      - OpenSSF/Securing Software Repo WG
      - `@github account`
    * - `<name>`
      - Voting Member
      - <Public Repository Name>
      - `@github account`

.. note::

  * Organizations could have more than 1 member, but they have the right to 1
    vote. It's recommended that organizations have more than one person listed
    in the TAC list to allow substitutions in the absence of a listed member.
  * A TAC member part of multiple organizations can vote for only one.

name: Lint

on:
  pull_request:
    branches: [ 'main' ]
    paths:
      - 'tests/**'
      - 'Pipfile*'
      - '.github/workflows/lint.yml'
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout release tag
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065
        with:
          python-version: '3.13'

      - name: Lint
        run: |
          pip install pipenv
          pipenv install -d
          pipenv run make lint
name: Functional Tests

on:
  workflow_dispatch:
      inputs:
        api_version:
          description: "API Version"
          default: "latest"
          type: string
          required: False
        worker_version:
          description: "Worker Version"
          default: "latest"
          type: string
          required: False
        cli_version:
          description: "CLI Version"
          default: "latest"
          type: string
          required: False
      docker_compose:
        description: "Docker Compose File"
        default: "docker-compose.yml"
        type: string
        required: False

  workflow_call:
    inputs:
      api_version:
        description: "API Version"
        default: "latest"
        type: string
        required: False
      worker_version:
        description: "Worker Version"
        default: "latest"
        type: string
        required: False
      cli_version:
        description: "CLI Version"
        default: "latest"
        type: string
        required: False
      docker_compose:
        description: "Docker Compose File"
        default: "docker-compose.yml"
        type: string
        required: False
    secrets:
      RSTUF_ONLINE_KEY:
        required: True
jobs:
  functional-das:
    name: "DAS"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        pytest-group: [ "1", "2", "3"]

    steps:
      - name: Checkout RSTUF Umbrella
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: RSTUF Deployment with docker compose
        uses: isbang/compose-action@8be2d741e891ac9b8ac20825e6f3904149599925
        with:
          compose-file: ${{ inputs.docker_compose }}
        env:
          API_VERSION: ${{ inputs.api_version }}
          WORKER_VERSION: ${{ inputs.worker_version }}
          RSTUF_ONLINE_KEY: ${{ secrets.RSTUF_ONLINE_KEY }}

      - name: Bootstrap/Setup RSTUF using DAS and run Functional Tests
        timeout-minutes: 30
        run: |
          docker compose run --env UMBRELLA_PATH=. --rm rstuf-ft-runner bash tests/functional/scripts/run-ft-das.sh ${{ inputs.cli_version }} ${{ matrix.pytest-group }}

  functional-signed:
    name: "Full Signed"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        pytest-group: [ "1", "2", "3" ]

    steps:
      - name: Checkout RSTUF Umbrella
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: RSTUF Deployment
        uses: isbang/compose-action@8be2d741e891ac9b8ac20825e6f3904149599925
        with:
          compose-file: ${{ inputs.docker_compose }}
        env:
          API_VERSION: ${{ inputs.api_version }}
          WORKER_VERSION: ${{ inputs.worker_version }}
          # If you use "RSTUF_ONLINE_KEY" make sure it's
          # defined in the repositories you use for prs.
          RSTUF_ONLINE_KEY: ${{ secrets.RSTUF_ONLINE_KEY }}

      - name: Bootstrap/Setup RSTUF full signed and run Functional Tests
        timeout-minutes: 30
        run: |
          docker compose run --env UMBRELLA_PATH=. --rm rstuf-ft-runner bash tests/functional/scripts/run-ft-signed.sh ${{ inputs.cli_version }} ${{ matrix.pytest-group }}

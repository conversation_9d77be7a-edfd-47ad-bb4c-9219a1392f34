name: CI

on:
  push:
    branches:
      - main
    paths-ignore:
      - 'docs/**'
      - "TAC.rst"
      - "CONTRIBUTING.rst"
      - "CODE_OF_CONDUCT.rst"
      - "LICENSE"
      - "MAINTAINERS.rst"
      - "README.rst"
      - "ROADMAP.rst"

  pull_request:
    paths-ignore:
      - 'docs/**'
      - "TAC.rst"
      - "CONTRIBUTING.rst"
      - "CODE_OF_CONDUCT.rst"
      - "LICENSE"
      - "MAINTAINERS.rst"
      - "README.rst"
      - "ROADMAP.rst"

  workflow_dispatch:

jobs:
  functional-tests:
    uses: ./.github/workflows/functional.yml
    with:
      api_version: dev
      worker_version: dev
      cli_version: dev
    secrets:
      RSTUF_ONLINE_KEY: ${{ secrets.RSTUF_ONLINE_KEY }}

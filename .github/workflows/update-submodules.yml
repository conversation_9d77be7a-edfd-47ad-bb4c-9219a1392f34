name: Sync submodules and RSTUF docs
on:
  workflow_dispatch:
  schedule:
    - cron: "0 8 * * *"
jobs:
  functional-tests:
    uses: ./.github/workflows/functional.yml
    with:
      api_version: dev
      worker_version: dev
      cli_version: dev
    secrets:
      RSTUF_ONLINE_KEY: ${{ secrets.RSTUF_ONLINE_KEY }}
  sync-submodules:
    name: "Sync submodules and RSTUF docs"
    runs-on: ubuntu-latest
    needs: functional-tests
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      # Install the dependencies and update plantuml.
      # The Ubuntu's plantuml version is old and doesn't support some features
      # used by RSTUF.
      - name: Install Dependencies and update plantuml
        run: |
          pip install pipenv
          pipenv install -d
          sudo apt update
          sudo apt install plantuml -y
          sudo wget -O /usr/share/plantuml/plantuml.jar https://github.com/plantuml/plantuml/releases/download/v1.2022.14/plantuml-1.2022.14.jar
      - name: Sync git submodules
        run: |
          make sync-submodules
      - name: Build docs
        run: |
          pipenv run make docs
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: sync git submodules and docs"
          branch: "rstuf-bot/update-submodules-docs"
          delete-branch: true
          title: "chore: sync git submodules and docs"
          body: >
            The following PR updates the submodule and RSTUF documentation references in the umbrella repository for repository-service-tuf-api, repository-service-tuf-cli and repository-service-tuf-worker.
          labels: automated pr
